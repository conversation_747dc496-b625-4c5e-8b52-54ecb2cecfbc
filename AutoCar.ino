#include <MsTimer2.h>
// 红外传感器引脚定义
const int SENSOR1 = A0;
const int SENSOR2 = A1;
const int SENSOR3 = A2;
const int SENSOR4 = A3;
const int SENSOR5 = A4;
const int SENSOR6 = A5;
const int SENSOR7 = A6;
const int SENSOR8 = A7;

// 电机控制引脚
#define ENCODER_A_L  2
#define ENCODER_B_L  4
#define ENCODER_A_R  3
#define ENCODER_B_R  5
#define PWML        11
#define PWMR        12
#define DIR_LEFT    6
#define DIR_RIGHT   7

// 控制参数
#define PERIOD      20
#define BASE_SPEED  30
#define MAX_SPEED   60
#define MIN_SPEED   -10
#define MAX_PWM     255

// [新增] PWM滑动滤波参数
#define PWM_FILTER_SIZE  5     // 滑动滤波窗口大小
#define PWM_FILTER_ALPHA 0.3   // 低通滤波系数 (0-1, 越小越平滑)

// [新增] 直角转弯参数
#define HISTORY_SIZE     5     // 历史状态记录深度(增加到5)
#define LOST_THRESHOLD  25    // 丢失轨迹阈值(约500ms)
#define SHARP_TURN_DIFF 40    // 直角转弯速度差(稍微减小)
#define MIN_ACTIVE_SENSORS 2  // 最少激活传感器数量

// 全局变量
volatile float TARGET_L = 0;
volatile float TARGET_R = 0;
volatile float encoderVal_L = 0;
volatile float encoderVal_R = 0;
volatile float velocity_L = 0;
volatile float velocity_R = 0;

// 增量式PID结构体
typedef struct {
  double Kp;              // 比例系数
  double Ki;              // 积分系数
  double Kd;              // 微分系数
  double prev_error;      // 上一次误差
  double prev_prev_error; // 上上次误差
  double output;          // 当前输出值
} IncrementalPID;

// [新增] PWM滑动滤波结构体
typedef struct {
  float buffer[PWM_FILTER_SIZE];  // 滑动窗口缓冲区
  int index;                      // 当前索引
  float sum;                      // 当前总和
  bool filled;                    // 缓冲区是否已填满
  float filtered_value;           // 滤波后的值
} PWMFilter;

// 参数换算：原来 kp=2, TI=20, TD=10, T=20ms
// Kp = kp = 2
// Ki = kp * T / TI = 2 * 20 / 20 = 2
// Kd = kp * TD / T = 2 * 10 / 20 = 1
IncrementalPID pid_L = {3, 0.5, 2, 0, 0, 0};  // 左轮PID// 2 2 1
IncrementalPID pid_R = {3, 0.5, 2, 0, 0, 0};  // 右轮PID

int pwm_L = 0;
int pwm_R = 0;

// [新增] PWM滤波器
PWMFilter pwm_filter_L = {{0}, 0, 0, false, 0};
PWMFilter pwm_filter_R = {{0}, 0, 0, false, 0};

// 循迹相关变量
int sensorState[8] = {1, 1, 1, 1, 1, 1, 1, 1};
int lastTrackState = 0;
unsigned long lostTrackTime = 0;

// [新增] 直角转弯相关变量
int sensorHistory[HISTORY_SIZE][8]; // 传感器历史状态寄存器
int historyIndex = 0;               // 当前历史记录位置
int lostCounter = 0;                // 丢失轨迹计数器
int lastDeviation = 0;              // 上次的偏差方向(左:-1, 右:1)
bool isSharpTurn = false;           // 是否正在进行直角转弯
int stableTrackCounter = 0;         // 稳定跟踪计数器

// 编码器中断处理函数
void getEncoder_L() {
  if (digitalRead(ENCODER_A_L) == LOW) {
    encoderVal_L += (digitalRead(ENCODER_B_L) == LOW) ? -1 : 1;
  } else {
    encoderVal_L += (digitalRead(ENCODER_B_L) == LOW) ? 1 : -1;
  }
}

void getEncoder_R() {
  if (digitalRead(ENCODER_A_R) == LOW) {
    encoderVal_R += (digitalRead(ENCODER_B_R) == LOW) ? 1 : -1;
  } else {
    encoderVal_R += (digitalRead(ENCODER_B_R) == LOW) ? -1 : 1;
  }
}

// 计算增量型PID控制器输出
double compute_pid(IncrementalPID *pid, double setpoint, double actual_value) {
    // 计算当前误差
    double error = setpoint - actual_value;

    // 计算PID增量
    double delta_output = pid->Kp * (error - pid->prev_error)
                          + pid->Ki * error
                          + pid->Kd * (error - 2 * pid->prev_error + pid->prev_prev_error);

    // 更新输出
    pid->output += delta_output;

    // 输出限幅
    if (pid->output > MAX_PWM) pid->output = MAX_PWM;
    else if (pid->output < -MAX_PWM) pid->output = -MAX_PWM;

    // 更新历史误差
    pid->prev_prev_error = pid->prev_error;
    pid->prev_error = error;

    return pid->output;
}

// 左轮PID控制
int pidcontrol_L(float target, float current) {
  return (int)compute_pid(&pid_L, target, current);
}

// 右轮PID控制
int pidcontrol_R(float target, float current) {
  return (int)compute_pid(&pid_R, target, current);
}

void readSensors() {
  sensorState[0] = digitalRead(SENSOR1);
  sensorState[1] = digitalRead(SENSOR2);
  sensorState[2] = digitalRead(SENSOR3);
  sensorState[3] = digitalRead(SENSOR4);
  sensorState[4] = digitalRead(SENSOR5);
  sensorState[5] = digitalRead(SENSOR6);
  sensorState[6] = digitalRead(SENSOR7);
  sensorState[7] = digitalRead(SENSOR8);
  
  // 更新历史寄存器
  for(int i=0; i<8; i++) {
    sensorHistory[historyIndex][i] = sensorState[i];
  }
  historyIndex = (historyIndex + 1) % HISTORY_SIZE;
}

// [新增] 获取历史主要偏差方向
int getHistoryDeviation() {
  int leftCount = 0;
  int rightCount = 0;
  
  for(int i=0; i<HISTORY_SIZE; i++) {
    int sensorSum = 0;
    int activeSensors = 0;
    
    for(int j=0; j<8; j++) {
      if(sensorHistory[i][j] == 1) {
        sensorSum += (j - 3.5);
        activeSensors++;
      }
    }
    
    if(activeSensors > 0) {
      float deviation = sensorSum / (float)activeSensors;
      if(deviation > 0) rightCount++;
      else if(deviation < 0) leftCount++;
    }
  }
  
  if(leftCount > rightCount) return -1;
  else if(rightCount > leftCount) return 1;
  else return 0;
}

// [修改] 计算目标速度（增加直角转弯处理）
void calculateTargetSpeed() {
  int sensorSum = 0;
  int activeSensors = 0;
  
  for(int i=0; i<8; i++) {
    if(sensorState[i] == 1) {
      sensorSum += (i - 3.5);
      activeSensors++;
    }
  }
  
  // 处理丢失轨迹情况（直角转弯时）
  if(activeSensors == 0) {
    lostCounter++;
    
    // 根据历史状态判断转弯方向
    int histDev = getHistoryDeviation();
    if(histDev != 0) {
      lastDeviation = histDev;
    }
    
    // 根据最后已知方向进行转弯
    if(lostCounter < LOST_THRESHOLD) {
      if(lastDeviation < 0) { // 最后是左偏
        TARGET_L = BASE_SPEED - SHARP_TURN_DIFF;
        TARGET_R = BASE_SPEED + SHARP_TURN_DIFF;
      } else { // 最后是右偏
        TARGET_L = BASE_SPEED + SHARP_TURN_DIFF;
        TARGET_R = BASE_SPEED - SHARP_TURN_DIFF;
      }
    } else {
      TARGET_L = 0;
      TARGET_R = 0;
    }
    return;
  }
  
  // 重置丢失计数器
  lostCounter = 0;
  lastTrackState = 1;
  
  // 计算当前偏差
  float deviation = sensorSum / (float)activeSensors;
  lastDeviation = (deviation > 0) ? 1 : ((deviation < 0) ? -1 : 0);
  
  // 标准循迹逻辑
  float speedDiff = deviation * abs(deviation) * ((MAX_SPEED - BASE_SPEED) / 2.0);
  
  TARGET_L = BASE_SPEED - speedDiff;
  TARGET_R = BASE_SPEED + speedDiff;
  
  // 速度限幅
  TARGET_L = constrain(TARGET_L, MIN_SPEED, MAX_SPEED);
  TARGET_R = constrain(TARGET_R, MIN_SPEED, MAX_SPEED);
}

void controlMotors() {
  velocity_L = (encoderVal_L / 780.0) * 3.1415 * 2.0 * (1000.0 / PERIOD) * 10;
  velocity_R = (encoderVal_R / 780.0) * 3.1415 * 2.0 * (1000.0 / PERIOD) * 10;
  
  pwm_L = pidcontrol_L(TARGET_L, velocity_L);
  pwm_R = pidcontrol_R(TARGET_R, velocity_R);
  
  // 控制左电机
  if (pwm_L >= 0) {
    digitalWrite(DIR_LEFT, HIGH);
  } else {
    digitalWrite(DIR_LEFT, LOW);
  }
  analogWrite(PWML, abs(pwm_L));
  
  // 控制右电机
  if (pwm_R >= 0) {
    digitalWrite(DIR_RIGHT, LOW);
  } else {
    digitalWrite(DIR_RIGHT, HIGH);
  }
  analogWrite(PWMR, abs(pwm_R));
  
  encoderVal_L = 0;
  encoderVal_R = 0;
}

void controlTask() {
  readSensors();
  calculateTargetSpeed();
  controlMotors();
}

void setup() {
  // 配置PWM频率
  TCCR1B = TCCR1B & B11111000 | B00000001;
  
  // 初始化引脚
  pinMode(PWML, OUTPUT);
  pinMode(PWMR, OUTPUT);
  pinMode(DIR_LEFT, OUTPUT);
  pinMode(DIR_RIGHT, OUTPUT);
  pinMode(SENSOR1, INPUT);
  pinMode(SENSOR2, INPUT);
  pinMode(SENSOR3, INPUT);
  pinMode(SENSOR4, INPUT);
  pinMode(SENSOR5, INPUT);
  pinMode(SENSOR6, INPUT);
  pinMode(SENSOR7, INPUT);
  pinMode(SENSOR8, INPUT);
  pinMode(ENCODER_A_L, INPUT);
  pinMode(ENCODER_B_L, INPUT);
  pinMode(ENCODER_A_R, INPUT);
  pinMode(ENCODER_B_R, INPUT);
  
  // 配置中断
  attachInterrupt(0, getEncoder_L, CHANGE);
  attachInterrupt(1, getEncoder_R, CHANGE);
  
  // 初始化串口
  Serial.begin(9600);
  
  // [新增] 初始化历史寄存器
  for(int i=0; i<HISTORY_SIZE; i++) {
    for(int j=0; j<8; j++) {
      sensorHistory[i][j] = 1;
    }
  }
  
  // 启动定时器
  MsTimer2::set(PERIOD, controlTask);
  MsTimer2::start();
  
  digitalWrite(DIR_LEFT, LOW);
  digitalWrite(DIR_RIGHT, LOW);
}

void loop() {
  Serial.print("Sensors: ");
  // for(int i=0; i<8; i++) {
  //   Serial.print(sensorState[i]);
  // }
  // Serial.print(" | Target: L=");
  // Serial.print(TARGET_L);
  // Serial.print(", R=");
  // Serial.print(TARGET_R);
  // Serial.print(" | Vel: L=");
  // Serial.print(velocity_L, 2);
  // Serial.print(", R=");
  // Serial.print(velocity_R, 2);
  // Serial.print(" | PWM: L=");
  // Serial.print(pwm_L);
  // Serial.print(", R=");
  // Serial.print(pwm_R);
  // Serial.print(" | Lost: ");
  // Serial.print(lostCounter);
  // Serial.print(" | LastDev: ");
  // Serial.print(lastDeviation);
  // Serial.println("");
  
  // delay(200);
}